<?php
require_once '../includes/dbh.inc.php';

echo "<h2>Adding Missing Columns to Subjects Table</h2>";

// Check if credit_hours column exists
$checkCreditHours = $conn->query("SHOW COLUMNS FROM subjects LIKE 'credit_hours'");
if ($checkCreditHours->num_rows == 0) {
    $addCreditHours = "ALTER TABLE subjects ADD COLUMN credit_hours INT(2) DEFAULT 4";
    if ($conn->query($addCreditHours)) {
        echo "<p>✅ credit_hours column added successfully</p>";
    } else {
        echo "<p>❌ Error adding credit_hours column: " . $conn->error . "</p>";
    }
} else {
    echo "<p>✅ credit_hours column already exists</p>";
}

// Check if subject_type column exists
$checkSubjectType = $conn->query("SHOW COLUMNS FROM subjects LIKE 'subject_type'");
if ($checkSubjectType->num_rows == 0) {
    $addSubjectType = "ALTER TABLE subjects ADD COLUMN subject_type ENUM('compulsory', 'optional') DEFAULT 'optional'";
    if ($conn->query($addSubjectType)) {
        echo "<p>✅ subject_type column added successfully</p>";
    } else {
        echo "<p>❌ Error adding subject_type column: " . $conn->error . "</p>";
    }
} else {
    echo "<p>✅ subject_type column already exists</p>";
}

echo "<p><a href='subject_csv_upload.php'>Go to Subject CSV Upload</a></p>";

$conn->close();
?>
