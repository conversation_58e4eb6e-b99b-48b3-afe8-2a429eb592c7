<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create attendance table if it doesn't exist
$tableQuery = "CREATE TABLE IF NOT EXISTS attendance (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    class_id INT(11) NOT NULL,
    subject_id INT(11) DEFAULT NULL,
    date DATE NOT NULL,
    status ENUM('present', 'absent', 'late', 'excused') DEFAULT 'absent',
    remarks TEXT,
    recorded_by INT(11),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT unique_attendance UNIQUE (student_id, class_id, subject_id, date)
)";

$conn->query($tableQuery);

// Get classes for dropdown
$classesQuery = "SELECT id, class_name FROM classes ORDER BY class_name";
$classesResult = $conn->query($classesQuery);
$classes = [];
if ($classesResult && $classesResult->num_rows > 0) {
    while ($row = $classesResult->fetch_assoc()) {
        $classes[$row['id']] = $row['class_name'];
    }
}

// Get subjects for dropdown
$subjectsQuery = "SELECT id, subject_name FROM subjects ORDER BY subject_name";
$subjectsResult = $conn->query($subjectsQuery);
$subjects = [];
if ($subjectsResult && $subjectsResult->num_rows > 0) {
    while ($row = $subjectsResult->fetch_assoc()) {
        $subjects[$row['id']] = $row['subject_name'];
    }
}

// Handle form submission for taking attendance
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'take_attendance') {
        $class_id = intval($_POST['class_id']);
        $subject_id = !empty($_POST['subject_id']) ? intval($_POST['subject_id']) : null;
        $date = $_POST['attendance_date'];
        $recorded_by = $_SESSION['userId'];
        
        // Get all students for this class
        $studentsQuery = "SELECT id FROM students WHERE class_id = ?";
        $stmt = $conn->prepare($studentsQuery);
        $stmt->bind_param("i", $class_id);
        $stmt->execute();
        $studentsResult = $stmt->get_result();
        
        // Begin transaction
        $conn->begin_transaction();
        
        $success = true;
        
        while ($student = $studentsResult->fetch_assoc()) {
            $student_id = $student['id'];
            $status_field = "status_" . $student_id;
            $remarks_field = "remarks_" . $student_id;
            
            $status = isset($_POST[$status_field]) ? $_POST[$status_field] : 'absent';
            $remarks = isset($_POST[$remarks_field]) ? $conn->real_escape_string($_POST[$remarks_field]) : '';
            
            // Check if record already exists
            $checkQuery = "SELECT id FROM attendance 
                         WHERE student_id = ? AND class_id = ? AND date = ?";
            $stmt = $conn->prepare($checkQuery);
            if ($subject_id) {
                $checkQuery = "SELECT id FROM attendance 
                             WHERE student_id = ? AND class_id = ? AND subject_id = ? AND date = ?";
                $stmt = $conn->prepare($checkQuery);
                $stmt->bind_param("iiis", $student_id, $class_id, $subject_id, $date);
            } else {
                $stmt->bind_param("iis", $student_id, $class_id, $date);
            }
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                // Update existing record
                $row = $result->fetch_assoc();
                $attendance_id = $row['id'];
                
                if ($subject_id) {
                    $updateQuery = "UPDATE attendance 
                                  SET status = ?, remarks = ?, recorded_by = ? 
                                  WHERE id = ?";
                    $stmt = $conn->prepare($updateQuery);
                    $stmt->bind_param("ssii", $status, $remarks, $recorded_by, $attendance_id);
                } else {
                    $updateQuery = "UPDATE attendance 
                                  SET status = ?, remarks = ?, recorded_by = ? 
                                  WHERE id = ?";
                    $stmt = $conn->prepare($updateQuery);
                    $stmt->bind_param("ssii", $status, $remarks, $recorded_by, $attendance_id);
                }
            } else {
                // Insert new record
                if ($subject_id) {
                    $insertQuery = "INSERT INTO attendance 
                                  (student_id, class_id, subject_id, date, status, remarks, recorded_by) 
                                  VALUES (?, ?, ?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($insertQuery);
                    $stmt->bind_param("iiisssi", $student_id, $class_id, $subject_id, $date, $status, $remarks, $recorded_by);
                } else {
                    $insertQuery = "INSERT INTO attendance 
                                  (student_id, class_id, date, status, remarks, recorded_by) 
                                  VALUES (?, ?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($insertQuery);
                    $stmt->bind_param("iisssi", $student_id, $class_id, $date, $status, $remarks, $recorded_by);
                }
            }
            
            if (!$stmt->execute()) {
                $success = false;
                break;
            }
        }
        
        if ($success) {
            $conn->commit();
            $success_message = "উপস্থিতি সফলভাবে রেকর্ড করা হয়েছে।";
        } else {
            $conn->rollback();
            $error_message = "উপস্থিতি রেকর্ড করতে সমস্যা হয়েছে: " . $conn->error;
        }
    } else if (isset($_POST['action']) && $_POST['action'] === 'view_attendance') {
        $class_id = intval($_POST['class_id']);
        $subject_id = !empty($_POST['subject_id']) ? intval($_POST['subject_id']) : null;
        $date = $_POST['attendance_date'];
        
        // Redirect to the same page with GET parameters
        header("Location: attendance.php?class_id=$class_id&subject_id=$subject_id&date=$date&view=1");
        exit();
    }
}

// Get students and attendance records if viewing
$students = [];
$attendance_records = [];

if (isset($_GET['view']) && $_GET['view'] == 1) {
    $class_id = intval($_GET['class_id']);
    $date = $_GET['date'];
    $subject_id = isset($_GET['subject_id']) ? intval($_GET['subject_id']) : null;
    
    // Get students for this class
    $studentsQuery = "SELECT s.id, s.student_id as student_code, s.first_name, s.last_name 
                    FROM students s 
                    WHERE s.class_id = ? 
                    ORDER BY s.first_name, s.last_name";
    $stmt = $conn->prepare($studentsQuery);
    $stmt->bind_param("i", $class_id);
    $stmt->execute();
    $studentsResult = $stmt->get_result();
    
    while ($row = $studentsResult->fetch_assoc()) {
        $students[] = $row;
    }
    
    // Get attendance records
    if ($subject_id) {
        $attendanceQuery = "SELECT student_id, status, remarks 
                          FROM attendance 
                          WHERE class_id = ? AND subject_id = ? AND date = ?";
        $stmt = $conn->prepare($attendanceQuery);
        $stmt->bind_param("iis", $class_id, $subject_id, $date);
    } else {
        $attendanceQuery = "SELECT student_id, status, remarks 
                          FROM attendance 
                          WHERE class_id = ? AND date = ?";
        $stmt = $conn->prepare($attendanceQuery);
        $stmt->bind_param("is", $class_id, $date);
    }
    $stmt->execute();
    $attendanceResult = $stmt->get_result();
    
    while ($row = $attendanceResult->fetch_assoc()) {
        $attendance_records[$row['student_id']] = [
            'status' => $row['status'],
            'remarks' => $row['remarks']
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>উপস্থিতি ব্যবস্থাপনা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .attendance-form {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .status-present {
            background-color: #d4edda;
            color: #155724;
        }
        .status-absent {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-late {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-excused {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .attendance-table th, .attendance-table td {
            vertical-align: middle;
        }
    </style>

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css"></head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include('includes/sidebar.php'); ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">উপস্থিতি ব্যবস্থাপনা</h1>
                </div>
                
                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <!-- Attendance Filter Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-filter me-2"></i>উপস্থিতি ফিল্টার</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" class="row g-3">
                            <input type="hidden" name="action" value="view_attendance">
                            
                            <div class="col-md-3">
                                <label for="class_id" class="form-label">ক্লাস</label>
                                <select class="form-select" id="class_id" name="class_id" required>
                                    <option value="">ক্লাস নির্বাচন করুন</option>
                                    <?php foreach ($classes as $id => $name): ?>
                                    <option value="<?php echo $id; ?>" <?php echo (isset($_GET['class_id']) && $_GET['class_id'] == $id) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($name); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="subject_id" class="form-label">বিষয় (ঐচ্ছিক)</label>
                                <select class="form-select" id="subject_id" name="subject_id">
                                    <option value="">সকল বিষয়</option>
                                    <?php foreach ($subjects as $id => $name): ?>
                                    <option value="<?php echo $id; ?>" <?php echo (isset($_GET['subject_id']) && $_GET['subject_id'] == $id) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($name); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="attendance_date" class="form-label">তারিখ</label>
                                <input type="date" class="form-control" id="attendance_date" name="attendance_date" 
                                       value="<?php echo isset($_GET['date']) ? $_GET['date'] : date('Y-m-d'); ?>" required>
                            </div>
                            
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i> উপস্থিতি দেখুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <?php if (isset($_GET['view']) && count($students) > 0): ?>
                <!-- Attendance Taking Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5>
                                <i class="fas fa-clipboard-check me-2"></i>
                                <?php 
                                echo htmlspecialchars($classes[$_GET['class_id']]) . ' - উপস্থিতি ';
                                if (isset($_GET['subject_id']) && !empty($_GET['subject_id'])) {
                                    echo '(' . htmlspecialchars($subjects[$_GET['subject_id']]) . ')';
                                }
                                echo ' - ' . date('d/m/Y', strtotime($_GET['date']));
                                ?>
                            </h5>
                            <div>
                                <button type="button" class="btn btn-success btn-sm me-2" id="markAllPresent">
                                    <i class="fas fa-check me-1"></i> সবাইকে উপস্থিত মার্ক করুন
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" id="markAllAbsent">
                                    <i class="fas fa-times me-1"></i> সবাইকে অনুপস্থিত মার্ক করুন
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" id="attendanceForm">
                            <input type="hidden" name="action" value="take_attendance">
                            <input type="hidden" name="class_id" value="<?php echo $_GET['class_id']; ?>">
                            <input type="hidden" name="subject_id" value="<?php echo isset($_GET['subject_id']) ? $_GET['subject_id'] : ''; ?>">
                            <input type="hidden" name="attendance_date" value="<?php echo $_GET['date']; ?>">
                            
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover attendance-table">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="5%">#</th>
                                            <th width="15%">রোল/আইডি</th>
                                            <th width="20%">নাম</th>
                                            <th width="15%">উপস্থিতি</th>
                                            <th width="45%">মন্তব্য</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php 
                                        $i = 1;
                                        foreach ($students as $student): 
                                            $status = isset($attendance_records[$student['id']]) ? 
                                                     $attendance_records[$student['id']]['status'] : 'absent';
                                            $remarks = isset($attendance_records[$student['id']]) ? 
                                                      $attendance_records[$student['id']]['remarks'] : '';
                                        ?>
                                        <tr>
                                            <td><?php echo $i++; ?></td>
                                            <td><?php echo htmlspecialchars($student['student_code']); ?></td>
                                            <td><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></td>
                                            <td>
                                                <div class="d-flex">
                                                    <div class="form-check me-2">
                                                        <input class="form-check-input status-radio" type="radio" 
                                                               name="status_<?php echo $student['id']; ?>" 
                                                               id="present_<?php echo $student['id']; ?>" 
                                                               value="present" 
                                                               <?php echo $status == 'present' ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="present_<?php echo $student['id']; ?>">
                                                            উপস্থিত
                                                        </label>
                                                    </div>
                                                    <div class="form-check me-2">
                                                        <input class="form-check-input status-radio" type="radio" 
                                                               name="status_<?php echo $student['id']; ?>" 
                                                               id="absent_<?php echo $student['id']; ?>" 
                                                               value="absent" 
                                                               <?php echo $status == 'absent' ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="absent_<?php echo $student['id']; ?>">
                                                            অনুপস্থিত
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="d-flex mt-1">
                                                    <div class="form-check me-2">
                                                        <input class="form-check-input status-radio" type="radio" 
                                                               name="status_<?php echo $student['id']; ?>" 
                                                               id="late_<?php echo $student['id']; ?>" 
                                                               value="late" 
                                                               <?php echo $status == 'late' ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="late_<?php echo $student['id']; ?>">
                                                            দেরি
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input status-radio" type="radio" 
                                                               name="status_<?php echo $student['id']; ?>" 
                                                               id="excused_<?php echo $student['id']; ?>" 
                                                               value="excused" 
                                                               <?php echo $status == 'excused' ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="excused_<?php echo $student['id']; ?>">
                                                            ছুটি
                                                        </label>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" 
                                                       name="remarks_<?php echo $student['id']; ?>" 
                                                       placeholder="মন্তব্য লিখুন..." 
                                                       value="<?php echo htmlspecialchars($remarks); ?>">
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="text-center mt-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> উপস্থিতি সংরক্ষণ করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <?php elseif (isset($_GET['view'])): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> নির্বাচিত ক্লাসে কোন শিক্ষার্থী পাওয়া যায়নি।
                </div>
                <?php endif; ?>
                
                <!-- Attendance Report Section -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar me-2"></i>উপস্থিতি রিপোর্ট</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">উপস্থিতি রিপোর্ট দেখতে উপরের ফর্মে ক্লাস, বিষয় এবং তারিখ নির্বাচন করুন।</p>
                        
                        <div class="row mt-4">
                            <div class="col-md-4 mb-3">
                                <div class="card bg-primary bg-opacity-10 text-primary">
                                    <div class="card-body">
                                        <h5 class="card-title">মাসিক উপস্থিতি রিপোর্ট</h5>
                                        <p class="card-text">ক্লাস অনুযায়ী মাসিক উপস্থিতি রিপোর্ট দেখুন।</p>
                                        <a href="monthly_attendance_report.php" class="btn btn-sm btn-primary">রিপোর্ট দেখুন</a>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <div class="card bg-success bg-opacity-10 text-success">
                                    <div class="card-body">
                                        <h5 class="card-title">শিক্ষার্থী অনুযায়ী রিপোর্ট</h5>
                                        <p class="card-text">নির্দিষ্ট শিক্ষার্থীর উপস্থিতি রিপোর্ট দেখুন।</p>
                                        <a href="student_attendance_report.php" class="btn btn-sm btn-success">রিপোর্ট দেখুন</a>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <div class="card bg-info bg-opacity-10 text-info">
                                    <div class="card-body">
                                        <h5 class="card-title">বিষয় অনুযায়ী উপস্থিতি</h5>
                                        <p class="card-text">বিষয় অনুযায়ী উপস্থিতি রিপোর্ট দেখুন।</p>
                                        <a href="subject_attendance_report.php" class="btn btn-sm btn-info">রিপোর্ট দেখুন</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mark all students as present
            document.getElementById('markAllPresent').addEventListener('click', function() {
                const presentRadios = document.querySelectorAll('input[value="present"]');
                presentRadios.forEach(radio => {
                    radio.checked = true;
                });
            });
            
            // Mark all students as absent
            document.getElementById('markAllAbsent').addEventListener('click', function() {
                const absentRadios = document.querySelectorAll('input[value="absent"]');
                absentRadios.forEach(radio => {
                    radio.checked = true;
                });
            });
        });
    </script>
</body>
</html> 