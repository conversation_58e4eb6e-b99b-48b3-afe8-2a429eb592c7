<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize variables
$successMessage = '';
$errorMessage = '';
$subject = null;

// Check if subject ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header("Location: subjects.php");
    exit();
}

$subject_id = $_GET['id'];

// Handle form submission
if (isset($_POST['update_subject'])) {
    $subject_name = $_POST['subject_name'];
    $subject_code = $_POST['subject_code'];
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // Validate input
    if (empty($subject_name) || empty($subject_code)) {
        $errorMessage = "বিষয়ের নাম এবং কোড অবশ্যই পূরণ করতে হবে!";
    } else {
        // Check if subject code already exists for other subjects
        $checkQuery = "SELECT * FROM subjects WHERE subject_code = ? AND id != ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("si", $subject_code, $subject_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $errorMessage = "এই বিষয় কোড ইতিমধ্যে অন্য বিষয়ের জন্য ব্যবহৃত হয়েছে!";
        } else {
            // Update subject
            $updateQuery = "UPDATE subjects SET
                           subject_name = ?,
                           subject_code = ?,
                           is_active = ?
                           WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("ssii", $subject_name, $subject_code, $is_active, $subject_id);

            if ($stmt->execute()) {
                $successMessage = "বিষয় সফলভাবে আপডেট করা হয়েছে!";
            } else {
                $errorMessage = "বিষয় আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
    }
}

// Handle department types update
if (isset($_POST['update_department_types'])) {
    // Start transaction
    $conn->begin_transaction();

    try {
        // Get assigned departments and their types
        $departmentAssigned = $_POST['department_assigned'] ?? [];
        $departmentTypes = $_POST['department_type'] ?? [];

        // First, remove all existing department assignments for this subject
        $deleteQuery = "DELETE FROM subject_departments WHERE subject_id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $subject_id);
        $stmt->execute();

        // Also remove all department subject types for this subject
        $deleteTypesQuery = "DELETE FROM department_subject_types WHERE subject_id = ?";
        $stmt = $conn->prepare($deleteTypesQuery);
        $stmt->bind_param("i", $subject_id);
        $stmt->execute();

        // Insert new department assignments and types
        $insertDeptQuery = "INSERT INTO subject_departments (subject_id, department_id) VALUES (?, ?)";
        $stmtDept = $conn->prepare($insertDeptQuery);

        $insertTypeQuery = "INSERT INTO department_subject_types (subject_id, department_id, subject_type) VALUES (?, ?, ?)";
        $stmtType = $conn->prepare($insertTypeQuery);

        $assignedCount = 0;

        foreach ($departmentAssigned as $deptId => $assigned) {
            if ($assigned) {
                // Insert into subject_departments
                $stmtDept->bind_param("ii", $subject_id, $deptId);
                $stmtDept->execute();

                // Insert into department_subject_types
                $type = $departmentTypes[$deptId] ?? 'optional';
                $stmtType->bind_param("iis", $subject_id, $deptId, $type);
                $stmtType->execute();

                $assignedCount++;
            }
        }

        // Commit transaction
        $conn->commit();

        $successMessage = "বিষয়ের বিভাগ এবং ধরন সফলভাবে আপডেট করা হয়েছে! $assignedCount টি বিভাগে বিষয়টি যোগ করা হয়েছে।";
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        $errorMessage = "বিষয়ের বিভাগ এবং ধরন আপডেট করতে সমস্যা হয়েছে: " . $e->getMessage();
    }
}

// Get subject details
$subjectQuery = "SELECT s.*, d.department_name
                FROM subjects s
                LEFT JOIN departments d ON s.department_id = d.id
                WHERE s.id = ?";
$stmt = $conn->prepare($subjectQuery);
$stmt->bind_param("i", $subject_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header("Location: subjects.php");
    exit();
}

$subject = $result->fetch_assoc();

// Get departments for dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get subject types by department
$subjectTypesByDepartment = [];
$subjectTypesTableExists = $conn->query("SHOW TABLES LIKE 'department_subject_types'")->num_rows > 0;

if ($subjectTypesTableExists) {
    $typesQuery = "SELECT dst.department_id, d.department_name, dst.subject_type
                  FROM department_subject_types dst
                  JOIN departments d ON dst.department_id = d.id
                  WHERE dst.subject_id = ?";
    $stmt = $conn->prepare($typesQuery);
    $stmt->bind_param("i", $subject_id);
    $stmt->execute();
    $typesResult = $stmt->get_result();

    while ($typeRow = $typesResult->fetch_assoc()) {
        $deptId = $typeRow['department_id'];
        $deptName = $typeRow['department_name'];
        $subjectType = $typeRow['subject_type'];

        $typeLabel = '';
        $typeBadgeClass = '';

        if ($subjectType == 'required') {
            $typeLabel = 'আবশ্যিক';
            $typeBadgeClass = 'bg-primary';
        } elseif ($subjectType == 'optional') {
            $typeLabel = 'ঐচ্ছিক';
            $typeBadgeClass = 'bg-success';
        } else {
            $typeLabel = '৪র্থ';
            $typeBadgeClass = 'bg-warning text-dark';
        }

        $subjectTypesByDepartment[$deptId] = [
            'department_name' => $deptName,
            'subject_type' => $subjectType,
            'type_label' => $typeLabel,
            'badge_class' => $typeBadgeClass
        ];
    }
}

// Get subject types by group
$subjectTypesByGroup = [];
$subjectGroupsTableExists = $conn->query("SHOW TABLES LIKE 'subject_groups'")->num_rows > 0;

if ($subjectGroupsTableExists) {
    $groupTypesQuery = "SELECT sg.group_id, g.group_name, g.group_code, sg.subject_type
                      FROM subject_groups sg
                      JOIN groups g ON sg.group_id = g.id
                      WHERE sg.subject_id = ?";
    $stmt = $conn->prepare($groupTypesQuery);
    $stmt->bind_param("i", $subject_id);
    $stmt->execute();
    $groupTypesResult = $stmt->get_result();

    while ($typeRow = $groupTypesResult->fetch_assoc()) {
        $groupId = $typeRow['group_id'];
        $groupName = $typeRow['group_name'];
        $groupCode = $typeRow['group_code'];
        $subjectType = $typeRow['subject_type'];

        $typeLabel = '';
        $typeBadgeClass = '';

        if ($subjectType == 'required') {
            $typeLabel = 'আবশ্যিক';
            $typeBadgeClass = 'bg-primary';
        } elseif ($subjectType == 'optional') {
            $typeLabel = 'ঐচ্ছিক';
            $typeBadgeClass = 'bg-success';
        } else {
            $typeLabel = '৪র্থ';
            $typeBadgeClass = 'bg-warning text-dark';
        }

        if (!isset($subjectTypesByGroup[$groupId])) {
            $subjectTypesByGroup[$groupId] = [
                'group_name' => $groupName,
                'group_code' => $groupCode,
                'types' => []
            ];
        }

        $subjectTypesByGroup[$groupId]['types'][] = [
            'subject_type' => $subjectType,
            'type_label' => $typeLabel,
            'badge_class' => $typeBadgeClass
        ];
    }
}

// Check if teacher_subjects table exists
$teacherAssignments = null;
$teacherTableExists = $conn->query("SHOW TABLES LIKE 'teacher_subjects'")->num_rows > 0;

if ($teacherTableExists) {
    // Get teacher assignments for this subject
    try {
        $teachersQuery = "SELECT ts.id, t.first_name, t.last_name, s.session_name
                         FROM teacher_subjects ts
                         JOIN teachers t ON ts.teacher_id = t.id
                         JOIN sessions s ON ts.session_id = s.id
                         WHERE ts.subject_id = ?
                         ORDER BY s.session_name, t.first_name";
        $stmt = $conn->prepare($teachersQuery);
        $stmt->bind_param("i", $subject_id);
        $stmt->execute();
        $teacherAssignments = $stmt->get_result();
    } catch (Exception $e) {
        // Handle error silently
        $teacherAssignments = null;
    }
}

// Check if student_subjects table exists
$studentSelections = null;
$studentCount = 0;
$studentTableExists = $conn->query("SHOW TABLES LIKE 'student_subjects'")->num_rows > 0;

if ($studentTableExists) {
    try {
        // Get student selections for this subject
        $studentsQuery = "SELECT ss.id, st.first_name, st.last_name, st.student_id as student_code,
                         c.class_name, d.department_name, s.session_name
                         FROM student_subjects ss
                         JOIN students st ON ss.student_id = st.id
                         JOIN classes c ON st.class_id = c.id
                         JOIN departments d ON st.department_id = d.id
                         JOIN sessions s ON ss.session_id = s.id
                         WHERE ss.subject_id = ?
                         ORDER BY s.session_name, st.first_name
                         LIMIT 10";
        $stmt = $conn->prepare($studentsQuery);
        $stmt->bind_param("i", $subject_id);
        $stmt->execute();
        $studentSelections = $stmt->get_result();

        // Count total students taking this subject
        $studentCountQuery = "SELECT COUNT(*) as total FROM student_subjects WHERE subject_id = ?";
        $stmt = $conn->prepare($studentCountQuery);
        $stmt->bind_param("i", $subject_id);
        $stmt->execute();
        $studentCountResult = $stmt->get_result();
        $studentCount = $studentCountResult->fetch_assoc()['total'];
    } catch (Exception $e) {
        // Handle error silently
        $studentSelections = null;
        $studentCount = 0;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>বিষয় সম্পাদনা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .card-hover {
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .info-card {
            border-left: 4px solid;
            transition: all 0.3s ease;
        }
        .info-card.active {
            border-left-color: #198754;
        }
        .info-card.inactive {
            border-left-color: #dc3545;
        }
        .assignment-card {
            transition: all 0.3s ease;
        }
        .assignment-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>বিষয় সম্পাদনা</h2>
                        <p class="text-muted">বিষয়ের তথ্য সম্পাদনা এবং সংশ্লিষ্ট তথ্য দেখুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="subjects.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>বিষয় তালিকায় ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Subject Information -->
                    <div class="col-md-4 mb-4">
                        <div class="card info-card <?php echo $subject['is_active'] ? 'active' : 'inactive'; ?> h-100">
                            <div class="card-header <?php echo $subject['is_active'] ? 'bg-success' : 'bg-danger'; ?> text-white">
                                <h5 class="card-title mb-0">বিষয় তথ্য</h5>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-4">
                                    <div class="display-1 mb-3">
                                        <i class="fas fa-book-open text-primary"></i>
                                    </div>
                                    <h4><?php echo htmlspecialchars($subject['subject_name']); ?></h4>
                                    <p class="text-muted"><?php echo htmlspecialchars($subject['subject_code']); ?></p>
                                    <span class="badge <?php echo $subject['is_active'] ? 'bg-success' : 'bg-danger'; ?> px-3 py-2">
                                        <?php echo $subject['is_active'] ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                    </span>
                                </div>

                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-building me-2 text-primary"></i> বিভাগ:</span>
                                        <span class="fw-bold"><?php echo htmlspecialchars($subject['department_name'] ?? 'N/A'); ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-chalkboard-teacher me-2 text-success"></i> শিক্ষক:</span>
                                        <span class="fw-bold"><?php echo $teacherAssignments ? $teacherAssignments->num_rows : 0; ?> জন</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-user-graduate me-2 text-info"></i> শিক্ষার্থী:</span>
                                        <span class="fw-bold"><?php echo $studentCount; ?> জন</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-calendar-alt me-2 text-warning"></i> তৈরি:</span>
                                        <span class="fw-bold"><?php echo date('d/m/Y', strtotime($subject['created_at'])); ?></span>
                                    </li>

                                    <?php if ($subjectTypesTableExists && !empty($subjectTypesByDepartment)): ?>
                                    <li class="list-group-item">
                                        <div class="mb-2">
                                            <i class="fas fa-sitemap me-2 text-primary"></i>
                                            <strong>বিভাগ অনুযায়ী বিষয়ের ধরন:</strong>
                                        </div>
                                        <?php foreach ($subjectTypesByDepartment as $deptId => $typeInfo): ?>
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="ms-3"><?php echo htmlspecialchars($typeInfo['department_name']); ?>:</span>
                                                <span class="badge <?php echo $typeInfo['badge_class']; ?>"><?php echo $typeInfo['type_label']; ?></span>
                                            </div>
                                        <?php endforeach; ?>
                                        <div class="mt-2">
                                            <a href="create_department_subject_types.php?subject_id=<?php echo $subject_id; ?>" class="btn btn-sm btn-outline-primary w-100">
                                                <i class="fas fa-edit me-1"></i> বিষয়ের ধরন পরিবর্তন করুন
                                            </a>
                                        </div>
                                    </li>
                                    <?php endif; ?>

                                    <?php if ($subjectGroupsTableExists && !empty($subjectTypesByGroup)): ?>
                                    <li class="list-group-item">
                                        <div class="mb-2">
                                            <i class="fas fa-layer-group me-2 text-warning"></i>
                                            <strong>গ্রুপ অনুযায়ী বিষয়ের ধরন:</strong>
                                        </div>
                                        <?php foreach ($subjectTypesByGroup as $groupId => $groupInfo): ?>
                                            <div class="mb-3">
                                                <div class="fw-bold ms-3"><?php echo htmlspecialchars($groupInfo['group_name']); ?> (<?php echo htmlspecialchars($groupInfo['group_code']); ?>):</div>
                                                <div class="ms-4 mt-1">
                                                    <?php foreach ($groupInfo['types'] as $typeInfo): ?>
                                                        <span class="badge <?php echo $typeInfo['badge_class']; ?> me-1"><?php echo $typeInfo['type_label']; ?></span>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                        <div class="mt-2">
                                            <a href="subject_groups.php?subject_id=<?php echo $subject_id; ?>" class="btn btn-sm btn-outline-warning w-100">
                                                <i class="fas fa-edit me-1"></i> গ্রুপ সংযোগ পরিবর্তন করুন
                                            </a>
                                        </div>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            <div class="card-footer">
                                <div class="d-grid gap-2">
                                    <a href="subject_assignment.php?subject_id=<?php echo $subject_id; ?>" class="btn btn-outline-primary">
                                        <i class="fas fa-user-check me-2"></i>শিক্ষক বরাদ্দ করুন
                                    </a>
                                    <a href="subject_selected_students.php?subject_id=<?php echo $subject_id; ?>" class="btn btn-outline-info">
                                        <i class="fas fa-users me-2"></i>শিক্ষার্থী তালিকা দেখুন
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Edit Form -->
                    <div class="col-md-8 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">বিষয় সম্পাদনা করুন</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="edit_subject.php?id=<?php echo $subject_id; ?>">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="subject_name" class="form-label">বিষয়ের নাম <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="subject_name" name="subject_name" value="<?php echo htmlspecialchars($subject['subject_name']); ?>" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="subject_code" class="form-label">বিষয় কোড <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="subject_code" name="subject_code" value="<?php echo htmlspecialchars($subject['subject_code']); ?>" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="is_active" class="form-label d-block">অবস্থা</label>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo $subject['is_active'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="is_active">সক্রিয়</label>
                                            </div>
                                        </div>
                                        <div class="col-12 mt-4">
                                            <button type="submit" name="update_subject" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>আপডেট করুন
                                            </button>
                                            <a href="subjects.php" class="btn btn-secondary ms-2">
                                                <i class="fas fa-times me-2"></i>বাতিল করুন
                                            </a>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Department Subject Types -->
                        <div class="card mt-4">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">বিভাগ অনুযায়ী বিষয়ের ধরন</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="edit_subject.php?id=<?php echo $subject_id; ?>" id="departmentTypesForm">
                                    <input type="hidden" name="update_department_types" value="1">
                                    <input type="hidden" name="subject_id" value="<?php echo $subject_id; ?>">

                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>বিভাগ</th>
                                                    <th>বিষয়ের ধরন</th>
                                                    <th class="text-center">অন্তর্ভুক্ত</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php if ($departments && $departments->num_rows > 0): ?>
                                                    <?php
                                                    // Reset the departments result pointer
                                                    $departments->data_seek(0);
                                                    while ($dept = $departments->fetch_assoc()):
                                                        $deptId = $dept['id'];
                                                        // Check if this department is assigned to this subject
                                                        $checkQuery = "SELECT COUNT(*) as count FROM subject_departments WHERE subject_id = ? AND department_id = ?";
                                                        $checkStmt = $conn->prepare($checkQuery);
                                                        $checkStmt->bind_param("ii", $subject_id, $deptId);
                                                        $checkStmt->execute();
                                                        $checkResult = $checkStmt->get_result();
                                                        $isAssigned = ($checkResult->fetch_assoc()['count'] > 0);
                                                        $currentType = $departmentTypes[$deptId] ?? 'optional';
                                                    ?>
                                                        <tr>
                                                            <td><?php echo htmlspecialchars($dept['department_name']); ?></td>
                                                            <td>
                                                                <select name="department_type[<?php echo $deptId; ?>]" class="form-select form-select-sm" <?php echo !$isAssigned ? 'disabled' : ''; ?>>
                                                                    <option value="required" <?php echo ($currentType == 'required') ? 'selected' : ''; ?>>আবশ্যিক</option>
                                                                    <option value="optional" <?php echo ($currentType == 'optional') ? 'selected' : ''; ?>>ঐচ্ছিক</option>
                                                                    <option value="fourth" <?php echo ($currentType == 'fourth') ? 'selected' : ''; ?>>৪র্থ বিষয়</option>
                                                                </select>
                                                            </td>
                                                            <td class="text-center">
                                                                <div class="form-check form-switch d-flex justify-content-center">
                                                                    <input class="form-check-input department-toggle" type="checkbox"
                                                                           name="department_assigned[<?php echo $deptId; ?>]"
                                                                           value="1"
                                                                           data-dept-id="<?php echo $deptId; ?>"
                                                                           <?php echo $isAssigned ? 'checked' : ''; ?>>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    <?php endwhile; ?>
                                                <?php else: ?>
                                                    <tr>
                                                        <td colspan="3" class="text-center">কোন বিভাগ পাওয়া যায়নি</td>
                                                    </tr>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="d-grid gap-2 mt-3">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-save me-2"></i>বিভাগ এবং ধরন আপডেট করুন
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Related Information -->
                <div class="row">
                    <!-- Teacher Assignments -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">বরাদ্দকৃত শিক্ষক</h5>
                            </div>
                            <div class="card-body">
                                <?php if ($teacherAssignments && $teacherAssignments->num_rows > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>শিক্ষকের নাম</th>
                                                    <th>সেশন</th>
                                                    <th>অ্যাকশন</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php while ($teacher = $teacherAssignments->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($teacher['first_name'] . ' ' . $teacher['last_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($teacher['session_name']); ?></td>
                                                        <td>
                                                            <a href="view_teacher.php?id=<?php echo $teacher['id']; ?>" class="btn btn-sm btn-info">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>এই বিষয়ের জন্য কোন শিক্ষক বরাদ্দ করা হয়নি।
                                    </div>
                                <?php endif; ?>

                                <div class="mt-3">
                                    <a href="subject_assignment.php?subject_id=<?php echo $subject_id; ?>" class="btn btn-success">
                                        <i class="fas fa-plus-circle me-2"></i>শিক্ষক বরাদ্দ করুন
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Student Selections -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">নির্বাচিত শিক্ষার্থী</h5>
                            </div>
                            <div class="card-body">
                                <?php if ($studentSelections && $studentSelections->num_rows > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>শিক্ষার্থীর নাম</th>
                                                    <th>আইডি</th>
                                                    <th>ক্লাস</th>
                                                    <th>সেশন</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php while ($student = $studentSelections->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($student['student_code']); ?></td>
                                                        <td><?php echo htmlspecialchars($student['class_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($student['session_name']); ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>

                                    <?php if ($studentCount > 10): ?>
                                        <div class="alert alert-info mt-3">
                                            <i class="fas fa-info-circle me-2"></i>আরও <?php echo $studentCount - 10; ?> জন শিক্ষার্থী এই বিষয় নির্বাচন করেছে।
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>এই বিষয়টি কোন শিক্ষার্থী নির্বাচন করেনি।
                                    </div>
                                <?php endif; ?>

                                <div class="mt-3">
                                    <a href="subject_selected_students.php?subject_id=<?php echo $subject_id; ?>" class="btn btn-info">
                                        <i class="fas fa-users me-2"></i>সকল শিক্ষার্থী দেখুন
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert-dismissible');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);

            // Handle department toggle checkboxes
            const departmentToggles = document.querySelectorAll('.department-toggle');
            departmentToggles.forEach(function(toggle) {
                toggle.addEventListener('change', function() {
                    const deptId = this.getAttribute('data-dept-id');
                    const typeSelect = document.querySelector(`select[name="department_type[${deptId}]"]`);

                    if (this.checked) {
                        typeSelect.disabled = false;
                    } else {
                        typeSelect.disabled = true;
                    }
                });
            });
        });
    </script>
</body>
</html>
