<?php
// Get the current page filename
$currentPage = basename($_SERVER['PHP_SELF']);
?>

<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
    <div class="position-sticky sidebar-sticky pt-3">
        <div class="sidebar-brand mb-3 text-center">
            <h5 class="text-white mb-0" style="font-size: 1.2rem;">স্কুল ম্যানেজমেন্ট</h5>
            <small class="text-white-50">অ্যাডমিন প্যানেল</small>
        </div>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'dashboard.php') ? 'active' : ''; ?>" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'students.php') ? 'active' : ''; ?>" href="students.php">
                    <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী তালিকা
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'csv_upload.php') ? 'active' : ''; ?>" href="csv_upload.php">
                    <i class="fas fa-file-csv me-2"></i> CSV আপলোড <span class="badge bg-success rounded-pill ms-1">নতুন</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'student_subjects_upload.php') ? 'active' : ''; ?>" href="student_subjects_upload.php">
                    <i class="fas fa-book me-2"></i> বিষয় নির্বাচন CSV <span class="badge bg-success rounded-pill ms-1">সহজ</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'promote_students.php') ? 'active' : ''; ?>" href="promote_students.php">
                    <i class="fas fa-arrow-up me-2"></i> শিক্ষার্থী উন্নীতকরণ
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'teachers.php') ? 'active' : ''; ?>" href="teachers.php">
                    <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'classes.php') ? 'active' : ''; ?>" href="classes.php">
                    <i class="fas fa-school me-2"></i> শ্রেণী
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'exams.php' || $currentPage == 'exam_dashboard.php') ? 'active' : ''; ?>" href="http://localhost/zfaw/admin/exam_dashboard.php">
                    <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                </a>
            </li>

            <!-- পরিচালনা পর্ষদ মেনু আইটেম -->
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'committee.php') ? 'active' : ''; ?>" href="committee.php">
                    <i class="fas fa-users me-2"></i> পরিচালনা পর্ষদ
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'notices.php') ? 'active' : ''; ?>" href="notices.php">
                    <i class="fas fa-bullhorn me-2"></i> নোটিশ
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'fee_') !== false || $currentPage == 'due_fees.php') ? 'active collapsed' : 'collapsed'; ?>" href="#" data-bs-toggle="collapse" data-bs-target="#feeSubmenu" aria-expanded="<?php echo (strpos($_SERVER['REQUEST_URI'], 'fee_') !== false || $currentPage == 'due_fees.php') ? 'true' : 'false'; ?>">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    <span>ফি ম্যানেজমেন্ট</span>
                    <i class="fas fa-angle-down float-end mt-1"></i>
                </a>
                <div class="collapse <?php echo (strpos($_SERVER['REQUEST_URI'], 'fee_') !== false || $currentPage == 'due_fees.php') ? 'show' : ''; ?>" id="feeSubmenu">
                    <ul class="btn-toggle-nav list-unstyled fw-normal small">
                        <li><a href="fee_management.php" class="nav-link <?php echo ($currentPage == 'fee_management.php') ? 'active' : ''; ?>"><i class="fas fa-money-check-alt me-2"></i>ফি ম্যানেজমেন্ট</a></li>
                        <li><a href="due_fees.php" class="nav-link <?php echo ($currentPage == 'due_fees.php') ? 'active' : ''; ?>"><i class="fas fa-exclamation-circle me-2"></i>বকেয়া বেতন <span class="badge bg-danger rounded-pill ms-1">নতুন</span></a></li>
                        <li><a href="fee_types.php" class="nav-link <?php echo ($currentPage == 'fee_types.php') ? 'active' : ''; ?>"><i class="fas fa-tags me-2"></i>ফি টাইপ</a></li>
                        <li><a href="fee_categories.php" class="nav-link <?php echo ($currentPage == 'fee_categories.php') ? 'active' : ''; ?>"><i class="fas fa-folder me-2"></i>ফি ক্যাটাগরি</a></li>
                        <li><a href="fee_report.php" class="nav-link <?php echo ($currentPage == 'fee_report.php') ? 'active' : ''; ?>"><i class="fas fa-file-alt me-2"></i>ফি রিপোর্ট</a></li>
                    </ul>
                </div>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'sms_') !== false) ? 'active collapsed' : 'collapsed'; ?>" href="#" data-bs-toggle="collapse" data-bs-target="#smsSubmenu" aria-expanded="<?php echo (strpos($_SERVER['REQUEST_URI'], 'sms_') !== false) ? 'true' : 'false'; ?>">
                    <i class="fas fa-sms me-2"></i>
                    <span>SMS সার্ভিস</span>
                    <i class="fas fa-angle-down float-end mt-1"></i>
                </a>
                <div class="collapse <?php echo (strpos($_SERVER['REQUEST_URI'], 'sms_') !== false) ? 'show' : ''; ?>" id="smsSubmenu">
                    <ul class="btn-toggle-nav list-unstyled fw-normal small">
                        <li><a href="sms_ai_tools.php" class="nav-link <?php echo ($currentPage == 'sms_ai_tools.php') ? 'active' : ''; ?>"><i class="fas fa-robot me-2"></i>SMS AI টুলস</a></li>
                        <li><a href="sms_setup.php" class="nav-link <?php echo ($currentPage == 'sms_setup.php') ? 'active' : ''; ?>"><i class="fas fa-cog me-2"></i>SMS সেটআপ</a></li>
                    </ul>
                </div>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'settings') !== false) ? 'active collapsed' : 'collapsed'; ?>" href="#" data-bs-toggle="collapse" data-bs-target="#settingsSubmenu" aria-expanded="<?php echo (strpos($_SERVER['REQUEST_URI'], 'settings') !== false) ? 'true' : 'false'; ?>">
                    <i class="fas fa-cog me-2"></i>
                    <span>সেটিংস</span>
                    <i class="fas fa-angle-down float-end mt-1"></i>
                </a>
                <div class="collapse <?php echo (strpos($_SERVER['REQUEST_URI'], 'settings') !== false) ? 'show' : ''; ?>" id="settingsSubmenu">
                    <ul class="btn-toggle-nav list-unstyled fw-normal small">
                        <li><a href="settings.php" class="nav-link <?php echo ($currentPage == 'settings.php') ? 'active' : ''; ?>"><i class="fas fa-sliders-h me-2"></i>সাধারণ সেটিংস</a></li>
                        <li><a href="manage_heading.php" class="nav-link <?php echo ($currentPage == 'manage_heading.php') ? 'active' : ''; ?>"><i class="fas fa-heading me-2"></i>হেডিং ম্যানেজমেন্ট</a></li>
                        <li><a href="manage_signatures.php" class="nav-link <?php echo ($currentPage == 'manage_signatures.php') ? 'active' : ''; ?>"><i class="fas fa-signature me-2"></i>স্বাক্ষর ব্যবস্থাপনা</a></li>
                    </ul>
                </div>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="../includes/logout.inc.php">
                    <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                </a>
            </li>

      <li class="nav-item">
          <a href="tabulation_sheet.php" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'tabulation_sheet.php') ? 'active' : ''; ?>">
              <i class="fas fa-table me-2"></i> টেবুলেশন শীট
          </a>
      </li>
      <li class="nav-item">
          <a href="generate_seat_cards.php" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'generate_seat_cards.php') ? 'active' : ''; ?>">
              <i class="fas fa-id-card me-2"></i> সিট কার্ড তৈরি
          </a>
      </li>
      <li class="nav-item">
          <a href="student_exam_attendance.php" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'student_exam_attendance.php') ? 'active' : ''; ?>">
              <i class="fas fa-clipboard-check me-2"></i> শিক্ষার্থী হাজিরা পত্র
          </a>
      </li>
      <li class="nav-item">
          <a href="marks_entry.php" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'marks_entry.php') ? 'active' : ''; ?>">
              <i class="fas fa-edit me-2"></i> পরীক্ষার নম্বর এন্ট্রি
          </a>
      </li>
        </ul>

        <div class="sidebar-footer pt-3 pb-3 text-center" style="position: sticky; bottom: 0; left: 0; width: 100%; background: linear-gradient(to right, #1a2980, #26d0ce); border-top: 1px solid rgba(255, 255, 255, 0.1); margin-top: 20px;">
            <div>
                <a href="../includes/logout.inc.php" class="btn btn-sm btn-light">
                    <i class="fas fa-sign-out-alt me-1"></i> লগআউট
                </a>
            </div>
        </div>
    </div>
</nav>