<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Handle CSV template download
if (isset($_GET['download_template'])) {
    $filename = 'student_subjects_template_' . date('Y-m-d') . '.csv';
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    
    // Add UTF-8 BOM for proper Excel support
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Headers
    $headers = ['student_id', 'subject1_id', 'subject2_id', 'subject3_id', 'subject4_id', 'subject5_id', 'subject6_id', 'subject7_id', 'subject8_id'];
    
    // Sample data
    $sample_data = [
        ['STD-001', '1', '2', '3', '4', '', '', '', ''],
        ['STD-002', '1', '2', '3', '', '', '', '', ''],
        ['STD-003', '1', '2', '3', '4', '5', '', '', '']
    ];
    
    // Write headers
    fputcsv($output, $headers);
    
    // Write sample data
    foreach ($sample_data as $row) {
        fputcsv($output, $row);
    }
    
    fclose($output);
    exit();
}

// Handle CSV upload processing
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    if ($_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
        $file_extension = strtolower(pathinfo($_FILES['csv_file']['name'], PATHINFO_EXTENSION));
        
        if ($file_extension !== 'csv') {
            $errorMessage = 'অবৈধ ফাইল টাইপ। শুধুমাত্র CSV ফাইল অনুমোদিত।';
        } else {
            $csv_file = $_FILES['csv_file']['tmp_name'];
            
            // Read file content and handle UTF-8 BOM
            $content = file_get_contents($csv_file);
            if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
                $content = substr($content, 3);
            }
            
            // Create temporary file with cleaned content
            $temp_file = tempnam(sys_get_temp_dir(), 'csv_upload');
            file_put_contents($temp_file, $content);
            
            $handle = fopen($temp_file, 'r');
            
            if ($handle !== FALSE) {
                $headers = fgetcsv($handle); // Skip headers
                
                $success_count = 0;
                $error_count = 0;
                $errors = [];
                
                $conn->begin_transaction();
                
                try {
                    $row_number = 1;
                    
                    while (($data = fgetcsv($handle)) !== FALSE) {
                        $row_number++;
                        
                        // Skip empty rows
                        if (empty(array_filter($data))) {
                            continue;
                        }
                        
                        $student_id = !empty(trim($data[0])) ? trim($data[0]) : '';
                        
                        if (empty($student_id)) {
                            $error_count++;
                            $errors[] = "সারি $row_number: শিক্ষার্থী ID অনুপস্থিত";
                            continue;
                        }
                        
                        // Check if student exists
                        $student_check = $conn->prepare("SELECT id FROM students WHERE student_id = ?");
                        $student_check->bind_param("s", $student_id);
                        $student_check->execute();
                        $student_result = $student_check->get_result();
                        
                        if ($student_result->num_rows === 0) {
                            $error_count++;
                            $errors[] = "সারি $row_number: শিক্ষার্থী '$student_id' পাওয়া যায়নি";
                            continue;
                        }
                        
                        $student_data = $student_result->fetch_assoc();
                        $internal_student_id = $student_data['id'];
                        
                        // Extract subject IDs
                        $subject_ids = [];
                        for ($i = 1; $i < count($data); $i++) {
                            if (!empty(trim($data[$i])) && is_numeric(trim($data[$i]))) {
                                $subject_ids[] = intval($data[$i]);
                            }
                        }
                        
                        if (empty($subject_ids)) {
                            $error_count++;
                            $errors[] = "সারি $row_number: কোন বিষয় ID পাওয়া যায়নি";
                            continue;
                        }
                        
                        // Validate all subjects exist
                        $invalid_subjects = [];
                        foreach ($subject_ids as $subject_id) {
                            $subject_check = $conn->prepare("SELECT id FROM subjects WHERE id = ? AND is_active = 1");
                            $subject_check->bind_param("i", $subject_id);
                            $subject_check->execute();
                            if ($subject_check->get_result()->num_rows === 0) {
                                $invalid_subjects[] = $subject_id;
                            }
                        }
                        
                        if (!empty($invalid_subjects)) {
                            $error_count++;
                            $errors[] = "সারি $row_number: অবৈধ বিষয় ID: " . implode(', ', $invalid_subjects);
                            continue;
                        }
                        
                        // Delete existing subjects for this student
                        $delete_existing = $conn->prepare("DELETE FROM student_subjects WHERE student_id = ?");
                        $delete_existing->bind_param("i", $internal_student_id);
                        $delete_existing->execute();
                        
                        // Insert new subjects
                        $row_success = 0;
                        foreach ($subject_ids as $subject_id) {
                            $insert_query = "INSERT INTO student_subjects (student_id, subject_id, category) VALUES (?, ?, 'optional')";
                            $stmt = $conn->prepare($insert_query);
                            $stmt->bind_param("ii", $internal_student_id, $subject_id);
                            
                            if ($stmt->execute()) {
                                $row_success++;
                            }
                        }
                        
                        if ($row_success > 0) {
                            $success_count += $row_success;
                        }
                    }
                    
                    $conn->commit();
                    
                    if ($success_count > 0) {
                        $successMessage = "$success_count টি বিষয় নির্বাচন সফলভাবে আপডেট করা হয়েছে।";
                    }
                    
                    if ($error_count > 0) {
                        $errorMessage = "$error_count টি ত্রুটি হয়েছে। " . implode('<br>', array_slice($errors, 0, 10));
                        if (count($errors) > 10) {
                            $errorMessage .= "<br>... এবং আরও " . (count($errors) - 10) . " টি ত্রুটি";
                        }
                    }
                    
                } catch (Exception $e) {
                    $conn->rollback();
                    $errorMessage = 'CSV আপলোড করতে সমস্যা হয়েছে: ' . $e->getMessage();
                }
                
                fclose($handle);
                unlink($temp_file);
            } else {
                $errorMessage = 'CSV ফাইল পড়তে সমস্যা হয়েছে।';
            }
        }
    } else {
        $errorMessage = 'ফাইল আপলোড করতে সমস্যা হয়েছে।';
    }
}

// Get available subjects for reference
$subjects_query = "SELECT id, subject_name, subject_code FROM subjects WHERE is_active = 1 ORDER BY subject_name";
$subjects_result = $conn->query($subjects_query);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>শিক্ষার্থী বিষয় নির্বাচন CSV আপলোড - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/hind-siliguri.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include('includes/sidebar.php'); ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">📚 শিক্ষার্থী বিষয় নির্বাচন CSV আপলোড</h1>
                    <div>
                        <a href="students.php" class="btn btn-outline-primary">
                            <i class="fas fa-list me-1"></i> শিক্ষার্থী তালিকা
                        </a>
                    </div>
                </div>
                
                <!-- Instructions -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>নির্দেশনা:</strong> CSV ফাইলে শিক্ষার্থী ID এবং তাদের বিষয়গুলো আপলোড করুন।
                    <br><small><strong>ফরম্যাট:</strong> student_id, subject1_id, subject2_id, subject3_id, ...</small>
                    <br><small><strong>উদাহরণ:</strong> STD-001,1,2,3,4 (একজন শিক্ষার্থীর ৪টি বিষয়)</small>
                </div>
                
                <!-- Messages -->
                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>ত্রুটি!</strong> <?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>সফল!</strong> <?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Upload Form -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-upload me-2"></i>CSV ফাইল আপলোড</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="csv_file" class="form-label">CSV ফাইল নির্বাচন করুন</label>
                                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                                        <div class="form-text">ফরম্যাট: student_id, subject1_id, subject2_id, ...</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-upload me-2"></i>আপলোড করুন
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        
                        <div class="mt-3">
                            <a href="?download_template=1" class="btn btn-outline-secondary">
                                <i class="fas fa-download me-1"></i>টেমপ্লেট ডাউনলোড
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Subject Reference -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6><i class="fas fa-book me-2"></i>বিষয় তালিকা (রেফারেন্স)</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive" style="max-height: 400px;">
                            <table class="table table-sm table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>বিষয় ID</th>
                                        <th>বিষয়ের নাম</th>
                                        <th>বিষয় কোড</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($subjects_result && $subjects_result->num_rows > 0): ?>
                                        <?php while ($subject = $subjects_result->fetch_assoc()): ?>
                                        <tr>
                                            <td><strong><?php echo $subject['id']; ?></strong></td>
                                            <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                            <td><?php echo htmlspecialchars($subject['subject_code']); ?></td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="3" class="text-center text-muted">কোন বিষয় পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
