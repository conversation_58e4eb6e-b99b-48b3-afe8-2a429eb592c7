<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$errorMessage = '';
$successMessage = '';

// Handle CSV template download
if (isset($_GET['download_template'])) {
    $filename = 'subjects_template_' . date('Y-m-d') . '.csv';

    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    $output = fopen('php://output', 'w');

    // Add UTF-8 BOM for proper Excel support
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

    // Headers
    $headers = ['subject_name', 'subject_code', 'department_id', 'credit_hours', 'subject_type', 'is_active'];

    // Write headers
    fputcsv($output, $headers);

    // Get real data from database (first 10 subjects as examples)
    $real_data_query = "SELECT s.subject_name, s.subject_code, s.department_id,
                               4 as credit_hours,
                               CASE
                                   WHEN s.category = 'required' THEN 'compulsory'
                                   WHEN s.category = 'fourth' THEN 'optional'
                                   ELSE 'optional'
                               END as subject_type,
                               s.is_active
                        FROM subjects s
                        WHERE s.is_active = 1
                        ORDER BY s.subject_name
                        LIMIT 10";

    $real_data_result = $conn->query($real_data_query);

    if ($real_data_result && $real_data_result->num_rows > 0) {
        // Write real data from database
        while ($row = $real_data_result->fetch_assoc()) {
            $csv_row = [
                $row['subject_name'],
                $row['subject_code'],
                $row['department_id'] ?? '1',
                $row['credit_hours'],
                $row['subject_type'],
                $row['is_active']
            ];
            fputcsv($output, $csv_row);
        }
    } else {
        // Fallback sample data if no real data found
        $sample_data = [
            ['বাংলা', 'BAN-101', '1', '4', 'compulsory', '1'],
            ['ইংরেজি', 'ENG-101', '1', '4', 'compulsory', '1'],
            ['গণিত', 'MAT-101', '2', '4', 'compulsory', '1'],
            ['পদার্থবিজ্ঞান', 'PHY-101', '3', '4', 'optional', '1']
        ];

        foreach ($sample_data as $row) {
            fputcsv($output, $row);
        }
    }

    fclose($output);
    exit();
}

// Handle CSV upload processing
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    if ($_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
        $file_extension = strtolower(pathinfo($_FILES['csv_file']['name'], PATHINFO_EXTENSION));
        
        if ($file_extension !== 'csv') {
            $errorMessage = 'অবৈধ ফাইল টাইপ। শুধুমাত্র CSV ফাইল অনুমোদিত।';
        } else {
            $csv_file = $_FILES['csv_file']['tmp_name'];
            
            // Read file content and handle UTF-8 BOM
            $content = file_get_contents($csv_file);
            if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
                $content = substr($content, 3);
            }
            
            // Create temporary file with cleaned content
            $temp_file = tempnam(sys_get_temp_dir(), 'csv_upload');
            file_put_contents($temp_file, $content);
            
            $handle = fopen($temp_file, 'r');
            
            if ($handle !== FALSE) {
                $headers = fgetcsv($handle); // Skip headers
                
                $success_count = 0;
                $error_count = 0;
                $errors = [];
                
                $conn->begin_transaction();
                
                try {
                    $row_number = 1;
                    
                    while (($data = fgetcsv($handle)) !== FALSE) {
                        $row_number++;
                        
                        // Skip empty rows
                        if (empty(array_filter($data))) {
                            continue;
                        }
                        
                        $subject_name = !empty(trim($data[0])) ? trim($data[0]) : '';
                        $subject_code = !empty(trim($data[1])) ? trim($data[1]) : '';
                        $department_id = !empty(trim($data[2])) ? intval($data[2]) : 0;
                        $credit_hours = !empty(trim($data[3])) ? intval($data[3]) : 4;
                        $subject_type = !empty(trim($data[4])) ? trim($data[4]) : 'optional';
                        $is_active = !empty(trim($data[5])) ? intval($data[5]) : 1;
                        
                        // Validate required fields
                        if (empty($subject_name)) {
                            $error_count++;
                            $errors[] = "সারি $row_number: বিষয়ের নাম অনুপস্থিত";
                            continue;
                        }
                        
                        if (empty($subject_code)) {
                            $error_count++;
                            $errors[] = "সারি $row_number: বিষয় কোড অনুপস্থিত";
                            continue;
                        }
                        
                        if ($department_id <= 0) {
                            $error_count++;
                            $errors[] = "সারি $row_number: বৈধ বিভাগ ID প্রয়োজন";
                            continue;
                        }
                        
                        // Check if department exists
                        $dept_check = $conn->prepare("SELECT id FROM departments WHERE id = ?");
                        $dept_check->bind_param("i", $department_id);
                        $dept_check->execute();
                        if ($dept_check->get_result()->num_rows === 0) {
                            $error_count++;
                            $errors[] = "সারি $row_number: বিভাগ ID '$department_id' পাওয়া যায়নি";
                            continue;
                        }
                        
                        // Check if subject code already exists
                        $code_check = $conn->prepare("SELECT id FROM subjects WHERE subject_code = ?");
                        $code_check->bind_param("s", $subject_code);
                        $code_check->execute();
                        if ($code_check->get_result()->num_rows > 0) {
                            $error_count++;
                            $errors[] = "সারি $row_number: বিষয় কোড '$subject_code' ইতিমধ্যে বিদ্যমান";
                            continue;
                        }
                        
                        // Validate subject type
                        if (!in_array($subject_type, ['compulsory', 'optional'])) {
                            $subject_type = 'optional';
                        }
                        
                        // Convert subject_type to category for database compatibility
                        $category = ($subject_type === 'compulsory') ? 'required' : 'optional';

                        // Insert subject (using existing columns)
                        $insert_query = "INSERT INTO subjects (subject_name, subject_code, department_id, category, is_active, created_at) VALUES (?, ?, ?, ?, ?, NOW())";
                        $stmt = $conn->prepare($insert_query);
                        $stmt->bind_param("ssisi", $subject_name, $subject_code, $department_id, $category, $is_active);
                        
                        if ($stmt->execute()) {
                            $success_count++;
                        } else {
                            $error_count++;
                            $errors[] = "সারি $row_number: ডাটাবেসে সংরক্ষণ করতে সমস্যা";
                        }
                    }
                    
                    $conn->commit();
                    
                    if ($success_count > 0) {
                        $successMessage = "$success_count টি বিষয় সফলভাবে যোগ করা হয়েছে।";
                    }
                    
                    if ($error_count > 0) {
                        $errorMessage = "$error_count টি ত্রুটি হয়েছে। " . implode('<br>', array_slice($errors, 0, 10));
                        if (count($errors) > 10) {
                            $errorMessage .= "<br>... এবং আরও " . (count($errors) - 10) . " টি ত্রুটি";
                        }
                    }
                    
                } catch (Exception $e) {
                    $conn->rollback();
                    $errorMessage = 'CSV আপলোড করতে সমস্যা হয়েছে: ' . $e->getMessage();
                }
                
                fclose($handle);
                unlink($temp_file);
            } else {
                $errorMessage = 'CSV ফাইল পড়তে সমস্যা হয়েছে।';
            }
        }
    } else {
        $errorMessage = 'ফাইল আপলোড করতে সমস্যা হয়েছে।';
    }
}

// Get available departments for reference
$departments_query = "SELECT id, department_name FROM departments ORDER BY department_name";
$departments_result = $conn->query($departments_query);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <?php include 'includes/global-head.php'; ?>
    <title>বিষয় CSV আপলোড - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/hind-siliguri.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include('includes/sidebar.php'); ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">📚 বিষয় CSV আপলোড</h1>
                    <div>
                        <a href="subjects.php" class="btn btn-outline-primary">
                            <i class="fas fa-list me-1"></i> বিষয় তালিকা
                        </a>
                    </div>
                </div>
                
                <!-- Instructions -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>নির্দেশনা:</strong> CSV ফাইলে বিষয়ের তথ্য আপলোড করুন।
                    <br><small><strong>ফরম্যাট:</strong> subject_name, subject_code, department_id, credit_hours, subject_type, is_active</small>
                    <br><small><strong>subject_type:</strong> compulsory (বাধ্যতামূলক) অথবা optional (ঐচ্ছিক)</small>
                    <br><small><strong>is_active:</strong> 1 (সক্রিয়) অথবা 0 (নিষ্ক্রিয়)</small>
                    <br><small><strong>নোট:</strong> টেমপ্লেট ডাউনলোড করলে বিদ্যমান বিষয়গুলোর রিয়েল ডাটা পাবেন</small>
                </div>
                
                <!-- Messages -->
                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>ত্রুটি!</strong> <?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>সফল!</strong> <?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Upload Form -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-upload me-2"></i>CSV ফাইল আপলোড</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="csv_file" class="form-label">CSV ফাইল নির্বাচন করুন</label>
                                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                                        <div class="form-text">ফরম্যাট: subject_name, subject_code, department_id, credit_hours, subject_type, is_active</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-upload me-2"></i>আপলোড করুন
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        
                        <div class="mt-3">
                            <a href="?download_template=1" class="btn btn-outline-secondary">
                                <i class="fas fa-download me-1"></i>টেমপ্লেট ডাউনলোড
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Department Reference -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6><i class="fas fa-building me-2"></i>বিভাগ তালিকা (রেফারেন্স)</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive" style="max-height: 300px;">
                            <table class="table table-sm table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>বিভাগ ID</th>
                                        <th>বিভাগের নাম</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($departments_result && $departments_result->num_rows > 0): ?>
                                        <?php while ($department = $departments_result->fetch_assoc()): ?>
                                        <tr>
                                            <td><strong><?php echo $department['id']; ?></strong></td>
                                            <td><?php echo htmlspecialchars($department['department_name']); ?></td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="2" class="text-center text-muted">কোন বিভাগ পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
