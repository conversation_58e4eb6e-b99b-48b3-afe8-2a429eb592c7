<?php
// Get edit data if available
$edit_data = isset($edit_data) ? $edit_data : null;
?>

<style>
/* Fix loading issues */
body.loading,
body.loaded {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

#page-loader,
.loader,
.loading-spinner,
.loader-spinner,
.loader-text,
[id*="loader"],
[class*="loader"],
[class*="loading"],
.preloader,
.spinner,
[id*="preloader"],
[class*="preloader"],
[class*="spinner"] {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    height: 0 !important;
    width: 0 !important;
    position: absolute !important;
    z-index: -9999 !important;
    pointer-events: none !important;
}

/* Simple Exam Pattern CSS */
.exam-type-box {
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.exam-type-box:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.exam-type-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.exam-type-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.exam-type-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: white;
    font-size: 18px;
}

.exam-type-icon.cq {
    background-color: #4361ee;
}

.exam-type-icon.mcq {
    background-color: #2ecc71;
}

.exam-type-icon.practical {
    background-color: #3498db;
}

.exam-type-body {
    padding-top: 10px;
}

.marks-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.marks-input-group .form-control {
    flex: 1;
    font-size: 18px;
    text-align: center;
    font-weight: 500;
}

.marks-label {
    font-weight: 500;
    color: #666;
    min-width: 60px;
}

.marks-summary {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.marks-summary-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.marks-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.marks-card {
    flex: 1;
    min-width: 120px;
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.marks-card-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 5px;
}

.marks-card-value {
    font-size: 24px;
    font-weight: 700;
}

.marks-card.cq .marks-card-value {
    color: #4361ee;
}

.marks-card.mcq .marks-card-value {
    color: #2ecc71;
}

.marks-card.practical .marks-card-value {
    color: #3498db;
}

.marks-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    background-color: white;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.marks-total-label {
    font-weight: 600;
    font-size: 16px;
}

.marks-total-value {
    font-weight: 700;
    font-size: 20px;
    padding: 5px 15px;
    border-radius: 20px;
    color: white;
    background-color: #4361ee;
}

.marks-progress {
    height: 20px;
    background-color: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
}

.marks-progress-bar {
    height: 100%;
    float: left;
}

.marks-progress-bar.cq {
    background-color: #4361ee;
}

.marks-progress-bar.mcq {
    background-color: #2ecc71;
}

.marks-progress-bar.practical {
    background-color: #3498db;
}

.form-check-input:checked {
    background-color: #4361ee;
    border-color: #4361ee;
}
</style>

<div class="card animate__animated animate__fadeInLeft shadow">
    <div class="card-header bg-primary text-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-<?php echo $edit_data ? 'edit' : 'plus-circle'; ?> me-2"></i>
                <?php echo $edit_data ? 'পরীক্ষার প্যাটার্ন আপডেট করুন' : 'নতুন পরীক্ষার প্যাটার্ন যোগ করুন'; ?>
            </h5>
            <?php if ($edit_data): ?>
                <a href="subject_exam_pattern.php" class="btn btn-sm btn-light">
                    <i class="fas fa-plus-circle me-1"></i> নতুন যোগ করুন
                </a>
            <?php endif; ?>
        </div>
    </div>
    <div class="card-body">
        <form method="POST" action="" class="needs-validation" novalidate>
            <?php if ($edit_data): ?>
                <input type="hidden" name="edit_id" value="<?php echo $edit_data['id']; ?>">
            <?php endif; ?>

            <div class="mb-4">
                <label for="subject_id" class="form-label">
                    <i class="fas fa-book me-1 text-primary"></i> বিষয় নির্বাচন করুন
                </label>
                <select class="form-select" id="subject_id" name="subject_id" required>
                    <option value="">বিষয় নির্বাচন করুন</option>
                    <?php if ($subjects && $subjects->num_rows > 0): ?>
                        <?php while ($subject = $subjects->fetch_assoc()): ?>
                            <option value="<?php echo $subject['id']; ?>" <?php echo ($edit_data && $edit_data['subject_id'] == $subject['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($subject['subject_name'] . ' (' . $subject['subject_code'] . ')'); ?>
                            </option>
                        <?php endwhile; ?>
                    <?php endif; ?>
                </select>
                <div class="invalid-feedback">অনুগ্রহ করে একটি বিষয় নির্বাচন করুন।</div>
            </div>

            <div class="mb-4">
                <label for="total_marks" class="form-label">
                    <i class="fas fa-calculator me-1 text-primary"></i> মোট মার্কস
                </label>
                <input type="number" class="form-control" id="total_marks" name="total_marks" value="<?php echo $edit_data ? $edit_data['total_marks'] : '100'; ?>" min="1" required>
                <div class="invalid-feedback">অনুগ্রহ করে মোট মার্কস দিন।</div>
            </div>

            <h5 class="mb-3"><i class="fas fa-sliders-h me-2 text-primary"></i> পরীক্ষার ধরন</h5>

            <!-- CQ (Written) Exam Type -->
            <div class="exam-type-box">
                <div class="exam-type-header">
                    <div class="exam-type-title">
                        <div class="exam-type-icon cq">
                            <i class="fas fa-pen"></i>
                        </div>
                        <span>সিকিউ (লিখিত)</span>
                    </div>
                    <div class="form-check form-switch">
                        <input class="form-check-input component-toggle" type="checkbox" id="has_cq" name="has_cq" checked data-target="cq_marks">
                        <label class="form-check-label" for="has_cq">সক্রিয়</label>
                    </div>
                </div>
                <div class="exam-type-body">
                    <div class="marks-input-group">
                        <span class="marks-label">মার্কস:</span>
                        <input type="number" class="form-control distribution-component" id="cq_marks" name="cq_marks" value="<?php echo $edit_data ? $edit_data['cq_marks'] : '70'; ?>" min="0" step="0.01">
                    </div>
                </div>
            </div>

            <!-- MCQ Exam Type -->
            <div class="exam-type-box">
                <div class="exam-type-header">
                    <div class="exam-type-title">
                        <div class="exam-type-icon mcq">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <span>এমসিকিউ (বহুনির্বাচনী)</span>
                    </div>
                    <div class="form-check form-switch">
                        <input class="form-check-input component-toggle" type="checkbox" id="has_mcq" name="has_mcq" data-target="mcq_marks" <?php echo ($edit_data && $edit_data['has_mcq']) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="has_mcq">সক্রিয়</label>
                    </div>
                </div>
                <div class="exam-type-body">
                    <div class="marks-input-group">
                        <span class="marks-label">মার্কস:</span>
                        <input type="number" class="form-control distribution-component" id="mcq_marks" name="mcq_marks" value="<?php echo $edit_data ? $edit_data['mcq_marks'] : '30'; ?>" min="0" step="0.01" <?php echo ($edit_data && !$edit_data['has_mcq']) ? 'disabled' : ''; ?>>
                    </div>
                </div>
            </div>

            <!-- Practical Exam Type -->
            <div class="exam-type-box">
                <div class="exam-type-header">
                    <div class="exam-type-title">
                        <div class="exam-type-icon practical">
                            <i class="fas fa-flask"></i>
                        </div>
                        <span>ব্যবহারিক</span>
                    </div>
                    <div class="form-check form-switch">
                        <input class="form-check-input component-toggle" type="checkbox" id="has_practical" name="has_practical" data-target="practical_marks" <?php echo ($edit_data && $edit_data['has_practical']) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="has_practical">সক্রিয়</label>
                    </div>
                </div>
                <div class="exam-type-body">
                    <div class="marks-input-group">
                        <span class="marks-label">মার্কস:</span>
                        <input type="number" class="form-control distribution-component" id="practical_marks" name="practical_marks" value="<?php echo $edit_data ? $edit_data['practical_marks'] : '0'; ?>" min="0" step="0.01" <?php echo ($edit_data && !$edit_data['has_practical']) ? 'disabled' : ''; ?>>
                    </div>
                </div>
            </div>

            <div class="marks-summary">
                <h5 class="marks-summary-title"><i class="fas fa-chart-pie me-2"></i> মার্কস বিতরণ সারাংশ</h5>

                <div class="marks-cards">
                    <div class="marks-card cq" id="cq-card">
                        <div class="marks-card-title">সিকিউ (লিখিত)</div>
                        <div class="marks-card-value" id="cq-percentage">70%</div>
                    </div>
                    <div class="marks-card mcq" id="mcq-card">
                        <div class="marks-card-title">এমসিকিউ</div>
                        <div class="marks-card-value" id="mcq-percentage">30%</div>
                    </div>
                    <div class="marks-card practical" id="practical-card">
                        <div class="marks-card-title">ব্যবহারিক</div>
                        <div class="marks-card-value" id="practical-percentage">0%</div>
                    </div>
                </div>

                <div class="marks-total">
                    <div class="marks-total-label">মোট যোগফল:</div>
                    <div class="marks-total-value" id="total-sum">100</div>
                </div>

                <div class="marks-progress">
                    <div class="marks-progress-bar cq" id="cq-progress" style="width: 70%;"></div>
                    <div class="marks-progress-bar mcq" id="mcq-progress" style="width: 30%;"></div>
                    <div class="marks-progress-bar practical" id="practical-progress" style="width: 0%;"></div>
                </div>

                <div class="small text-muted">
                    <i class="fas fa-info-circle me-1"></i> সমস্ত উপাদানের যোগফল মোট মার্কসের সমান হতে হবে
                </div>
            </div>

            <div class="mb-3 mt-4">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo (!$edit_data || ($edit_data && $edit_data['is_active'] == 1)) ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="is_active">সক্রিয়</label>
                </div>
            </div>

            <div class="d-grid gap-2">
                <button type="submit" name="add_pattern" class="btn btn-primary btn-lg" id="submitButton">
                    <i class="fas fa-<?php echo $edit_data ? 'sync' : 'save'; ?> me-2"></i> <?php echo $edit_data ? 'আপডেট করুন' : 'সংরক্ষণ করুন'; ?>
                </button>

                <?php if ($edit_data): ?>
                    <a href="subject_exam_pattern.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i> বাতিল করুন
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>
</div>
